import { useCallback } from 'react';
import { toast } from 'sonner';
import { createAuthor } from '../../api';
import { useRootContext } from '../../context/useRootContext';
import { useAdminCounters } from '../../context/AdminCountersContext';
import { AppAuthor } from '../../core.types';
import { ManageAuthorForm } from './ManageAuthorForm';
import { ManageAuthorItem } from './ManageAuthorItem';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Users } from 'lucide-react';

export const ManageAuthors = () => {
  const { setAuthors, authors } = useRootContext();
  const { refreshCounters } = useAdminCounters();

  const onAuthorCreate = useCallback(
    async (values: AppAuthor) => {
      const isAlreadyAdded = authors.find(
        (author) => author.name === values.name,
      );

      if (isAlreadyAdded) {
        toast.error('Author is already added');
        return;
      }

      const tagId = await createAuthor(values as any);
      if (tagId) {
        setAuthors((prevAuthors: <AUTHORS>
          ...prevAuthors,
          { ...values, id: tagId } as AppAuthor,
        ]);
        // Refresh counters after successful creation
        refreshCounters();
      }
    },
    [authors, setAuthors, refreshCounters],
  );

  return (
    <div className='p-4 space-y-4'>
      <div className='grid grid-cols-1 lg:grid-cols-4 gap-4'>
        <div className='lg:col-span-1'>
          <Card>
            <CardHeader className='pb-3'>
              <CardTitle className='text-lg flex items-center gap-2 text-white'>
                <Users className='w-4 h-4' />
                Add Author
              </CardTitle>
            </CardHeader>
            <CardContent className='pt-0'>
              <ManageAuthorForm onSubmit={onAuthorCreate} />
            </CardContent>
          </Card>
        </div>

        <div className='lg:col-span-3'>
          <Card>
            <CardHeader className='pb-3'>
              <CardTitle className='text-lg text-white'>
                Authors ({authors.length})
              </CardTitle>
            </CardHeader>
            <CardContent className='pt-0'>
              {authors.length === 0 ? (
                <div className='text-center py-6 text-gray-500 text-sm'>
                  No authors found. Add your first author to get started.
                </div>
              ) : (
                <div className='grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-3'>
                  {authors.map((author, index) => (
                    <ManageAuthorItem key={index} author={author} />
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};
