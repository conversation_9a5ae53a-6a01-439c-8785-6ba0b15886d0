# SmartSecHub Resource Extraction Instructions for ChatGPT

## Overview

You are tasked with analyzing cybersecurity-related URLs and extracting structured information to create JSON resources for SmartSecHub. Follow these instructions carefully to generate properly formatted JSON data.

## Your Task

1. Visit each provided URL
2. Read and analyze the content
3. Extract relevant information
4. Generate a JSON array of resources following the specified schema

## JSON Schema Requirements

Each resource object must contain the following fields:

### Required Fields:

- **title** (string): The main title of the content
- **url** (string): The original URL provided
- **createdAt** (string): The date the content was created in `YYYY-MM-DD` format
- **tags** (array): THIS is really important field, analyze the content and add tags that describe the content. Use existing tags - I will provide them to you.
- **type** (string): One of the following values:
  - `"article"` - Blog posts, articles, written content
  - `"video"` - YouTube videos, video tutorials
  - `"tweet"` - Twitter/X posts
  - `"github"` - GitHub repositories, code projects
  - `"papers"` - Research papers, whitepapers, academic content
  - `"sites"` - Websites, tools, platforms

### Optional Fields:

- **customTwitterId** (string): If the author's Twitter ID is not in the database, add it here

### Fields to exclude:

- Do not include `id`, `likesCount`, `dislikesCount`,

## Content Analysis Guidelines

### Title Extraction:

- Use the actual title from the webpage/content
- If no clear title exists
- Keep titles concise but informative
- Remove unnecessary prefixes like "Blog:" or suffixes like "| Company Name"

### Type Classification:

- **article**: Blog posts, tutorials, guides, news articles, documentation
- **video**: Any video content (YouTube, Vimeo, etc.)
- **tweet**: Twitter/X posts and threads
- **github**: GitHub repositories, code samples, open source projects
- **papers**: Academic papers, research documents, whitepapers, technical reports
- **sites**: Tools, platforms, websites, online services, dashboards

## Output Format

Return a valid JSON array with this exact structure:

```json
[
  {
    "title": "Example Security Article Title",
    "url": "https://example.com/article",
    "type": "article",
    "createdAt": <current date>,
    "customTwitterId": <i can provide this>
    "authorId": <I will provide this>
    "tags": []
  },
  {
    "title": "Another Resource Title",
    "url": "https://example.com/video",
    "type": "video",
    "createdAt": <current date>,
    "customTwitterId": <i can provide this>
    "authorId": <I will provide this>
    "tags": []
  }
]
```

## Quality Standards

### Content Relevance:

Only include content related to:

- Cybersecurity
- Information security
- Penetration testing
- Vulnerability research
- Security tools and techniques
- Incident response
- Security architecture
- Compliance and governance
- Privacy and data protection

### Accuracy Requirements:

- Verify the URL is accessible
- Ensure the title matches the actual content
- Confirm the type classification is correct

## Error Handling

If a URL is inaccessible or contains irrelevant content:

- Skip that URL
- Do not include it in the JSON output
- Mention skipped URLs in a separate note after the JSON

## Example Input/Output

**Input URLs:**

- https://owasp.org/www-project-top-ten/
- https://github.com/danielmiessler/SecLists

**Expected Output:**

```json
[
  {
    "title": "OWASP Top 10 Web Application Security Risks",
    "url": "https://owasp.org/www-project-top-ten/",
    "type": "sites",
    "tags": []
  },
  {
    "title": "SecLists - Security Testing Lists",
    "url": "https://github.com/danielmiessler/SecLists",
    "type": "github",
    "tags": []
  }
]
```

## Final Notes

- Always return valid JSON that can be parsed
- Double-check URLs for typos
- Ensure all required fields are present
- Focus on cybersecurity relevance
- Maintain consistent formatting

---

**Instructions for use:** Paste the URLs you want to analyze after this prompt, and I will generate the JSON array following these specifications.

---

Input data:
{"authors":[{"id":"05O5N8FLvJVt2NbbS5bp","name":"Officer's Notes"},{"id":"0BuFbBg7xckDoikQIh1J","name":"Austin Griffith 🛠🚢🔥"},{"id":"0ocU5joMTCQvaK2om2IP","name":"Calyptus"},{"id":"17kJcdcNb1sN8X9m8J9a","name":"smlXL"},{"id":"1DmsElFzqNbXPhkfox7E","name":"ImmuneBytes"},{"id":"2EdvwDzkuhEE2wIY9Vc4","name":"zer0luck.eth"},{"id":"2L25356VAobKweejiNy0","name":"Quantstamp"},{"id":"2VDAF5PXqVbdM23SwjiI","name":"SolidityScan"},{"id":"2lXZ9Fa5xoNr1JcWuMAW","name":"RareSkills"},{"id":"2yRI5FZMEvFCD5oKs9HX","name":"OXORIO"},{"id":"3MHWDcEnwE2ke3FSwVSr","name":"KALOS"},{"id":"3Y1qhISuIQHZGg8JQ588","name":"Beosin Alert"},{"id":"3cypCCJ3YCoaPOMgJnjE","name":"theredguild"},{"id":"4D32PNprl5Kp9m3hrqkL","name":"Spearbit"},{"id":"4SjArSMLVK6rRpEyLMlH","name":"Trust"},{"id":"5z1h9rI8oBscdZSW92Ws","name":"@bytes032.xyz"},{"id":"6IwTW1UxeR9Gcvq60MzR","name":"PeckShield Inc."},{"id":"6KippZDF9ta6SzijNHHu","name":"Maxwell ꓘ Dulin (Strikeout)"},{"id":"6wyyyx2zrwRzPZqDULv4","name":"cmichel.io"},{"id":"","name":"Verichains"},{"id":"7w7cSFVNo4O6b1IUm624","name":"Composable Security"},{"id":"8g8GQTgdLp7QhoGA2PTi","name":"OpenZeppelin"},{"id":"AsCUoBiwakngX9pqozEL","name":"MixBytes"},{"id":"BLEwMWxb4TT5MBghUCv6","name":"BlockApex"},{"id":"BP8zavrYp5PanJnfpPwd","name":"Ackee Blockchain Security"},{"id":"BQLILU9V49cVCwBq9IIM","name":"Consensys Diligence"},{"id":"Bdp2ioGyFMo91aoHOo2h","name":"truscova"},{"id":"BixJYvxWpauQLntA48JE","name":"Marq"},{"id":"CHEb8JyOp6apO8sawyB7","name":"Halborn"},{"id":"CJINUPyXDXUpaVXQz1rD","name":"Macro Security"},{"id":"CKcUpFbeyZl1qEZxzpcG","name":"Aitor Zaldua"},{"id":"CPT39j3tzx2TWqgc8qlP","name":"horsefacts"},{"id":"CYTq8A6IoZMWoSkHz2uJ","name":"OpenSense"},{"id":"CauTqxcJaEZqLIAcin0D","name":"mrudenko"},{"id":"Cc4BSOqwJU8gLW3k77CL","name":"@0xVolodya"},{"id":"CeWZCxZlykvoxkMyUyK6","name":"Blaize"},{"id":"Cs9vfNMxfa8BPEb70SD5","name":"Ventral Digital"},{"id":"EGhlYPbH0QkKPLgssAoa","name":"Omniscia"},{"id":"F1QDecYFhJw0z8vExgPA","name":"Paradigm"},{"id":"FRENBLq67h49OvGhBEbv","name":"Ethereum Foundation"},{"id":"G8ICdwROZyMaVMwrkOkr","name":"DeGatchi"},{"id":"GER07GFX7KSJjAI3jiny","name":"Inspex"},{"id":"GUO0HSRkdpfcbwpBCeWB","name":"ChainLight"},{"id":"HslqCvo0XF99YeVm50qk","name":"kebabsec"},{"id":"HtAJ4kAYSvRivUmiMRnF","name":"Heuss"},{"id":"IJXLVd529WAGczqdE09P","name":"Trail of Bits"},{"id":"ILk1IQP5cmqA8ZZm0xun","name":"🐸Smart🐸Contract🐸Programmer🐸"},{"id":"J7eyTmxI3LDxal0mTf3x","name":"MiloTruck"},{"id":"JvjXhsGWf4Nq2AG7OAi1","name":"BountyHunt3r"},{"id":"K358lqYhtuQRWzBucwIn","name":"SHERLOCK"},{"id":"KM1rNklnMzfmIBMHIm0i","name":"hexens"},{"id":"KOJVqUgPZumMnpBEESCR","name":"Certik"},{"id":"KxIwGurJ2ED3LzNGWMKD","name":"HollaDieWaldfee"},{"id":"L0gXuPHQZSBCpYaEQiv8","name":"Dedaub"},{"id":"MJNWduIJ08B6pwbxxegA","name":"ChainSecurity"},{"id":"NPDcjFucPzGvwsKun0eO","name":"Veridise"},{"id":"OIU5cv4VWVMsIrkdDCCN","name":"George Hunter"},{"id":"OLVUYT2VDt79e7ChhuUm","name":"Olympix"},{"id":"OwLesOCiBtW9omAjykJx","name":"Callisto Network Official"},{"id":"P4nvlbribJIRSIygkw97","name":"Immunefi"},{"id":"Q6iugNd6vQRpUCMZT1cv","name":"Zellic"},{"id":"Qc3e35ktJYJP16KVofLb","name":"matta @ The Red Guild 🪷"},{"id":"T2r7OEcOqPgN9oDQOdX1","name":"Zaryab"},{"id":"THq0rdl7FVbupGKoJAWp","name":"zokyo 🇺🇦"},{"id":"TcBml4on1UBXkZp8wai0","name":"HACxyk."},{"id":"TgROws05K461fFRgx43O","name":"Rektoff"},{"id":"TpW247bPPIh1smSVDZFz","name":"Guillaume Lambert | lambert.eth | 🦇🔊"},{"id":"","name":"Eocene | Mixer Insights"},{"id":"VKKrpG91IT1mebaQLiSo","name":"StErMi"},{"id":"Wf5eNyGjYzqddrNtdiF4","name":"SlowMist"},{"id":"WyTElcqFIIj28hUIZrzK","name":"Decurity"},{"id":"XbB4XWANMljecus2XgE6","name":"Markus Waas"},{"id":"Xczvbx7LJhbbzNQhoBpz","name":"Web3 Security News"},{"id":"XvuTaF2KZSsyE2ZiTCfK","name":"dravee.eth"},{"id":"XzwdfRufex6OXDBLE5Fi","name":"Hacken🇺🇦"},{"id":"YK4I8jk8NqwMg3jCzXhW","name":"jeiwan.eth 🦇"},{"id":"aXgzApo2SzARMAYlY194","name":"HashEx DeFi Intelligence"},{"id":"aa9yzb3ippGflklNXcEm","name":"Sigma Prime"},{"id":"aodGGrMIjMhJUA3woXCd","name":"SunSec"},{"id":"bf0qGKiZxIr3s34XAg3T","name":"Oak Security"},{"id":"bfMcmoLyfaJtAFcIBtlM","name":"Sec3"},{"id":"bubLF1HXxKq3cyaOHVz7","name":"Fuzzy"},{"id":"cQAqSNhaioMME2w146sv","name":"J4X"},{"id":"cuQaX2AMArsADqzUIlKz","name":"kaden.eth"},{"id":"dFcKfe33OtOFYT2xIK2Q","name":"ddimitrov22"},{"id":"dOjGjPR7mqXPuUBkv5fF","name":"Alex Babits"},{"id":"dfy8cq00ARVJ96vqcbjs","name":"Alchemy | Powering Web3⚡️"},{"id":"dzlUhFuMZOlSBPaXVaCY","name":"Recon"},{"id":"e4Ro5F3zD7JQLjHVYmLE","name":"Jose María De la Cruz"},{"id":"ekfY4JTiM9gPeKUFNCuj","name":"FuzzingLabs"},{"id":"fbR3jh1hOsq426LvLNTk","name":"Beirao.xyz | Recudizoor 🕵️‍♂️"},{"id":"feUavaXW14uCbeAvkfc3","name":"Dimitar Tsvetanov"},{"id":"iTzlmQFe2OWH4oiHckfT","name":"33Audits"},{"id":"ifdrAvonDLXsKn5z6hPv","name":"Numen Cyber"},{"id":"jn3vxaukjZNTtXIglXb6","name":"SΞCURΞUM"},{"id":"jrhWYURRDZOj8IlevtOH","name":"Sayfer"},{"id":"lH19YrtljbzCcJx57x3V","name":"BlockSec"},{"id":"lNqf3bdR1fm1kDtZMduE","name":"PWNING"},{"id":"larUtAkmmWnnzHSzDLJv","name":"Johny Time"},{"id":"mDXXnVAwY1oDrgG5dbZc","name":"Certora"},{"id":"mt9gD4taBAAfqIsLrNEn","name":"SharkTeam"},{"id":"obxfV0mcOnyvmtLwVQY9","name":"Dacian"},{"id":"owWD7CnVbUiatJVPTPhe","name":"bloqarl"},{"id":"pAuU5QIk0eStI1nZIaao","name":"Valix Consulting"},{"id":"pJjqWX9EMapP4vz0tHFu","name":"AuditOne"},{"id":"pMgW9Uh6bVoXLyB2B72X","name":"Eocene | Security"},{"id":"qYgumReaZdMQ0mqo8kcQ","name":"Shieldify Security"},{"id":"qxJDfowcIDj0eLu35rav","name":"serial-coder (Phuwanai Thummavet)"},{"id":"tIZjtkjhqzNhNvRofdnj","name":"Arbitrary Execution"},{"id":"tV760wRSy4fszaQ6fcBD","name":"Akshay Srivastav"},{"id":"tsZG58oCNqcnqJnSs5Je","name":"iosiro"},{"id":"viaPHmA9Zmq0zaHvOp0m","name":"Runtime Vеrification"},{"id":"vrx6zCcxalz1GJqq9ToF","name":"Owen | Guardian Audits 🛡️"},{"id":"wCCWpTRTYDxB63bGDXz7","name":"Code4rena"},{"id":"wDgAbrr4oM3zJiwC9gTd","name":"Antonio Viggiano"},{"id":"wJ3wSnZUyaUTViawdYWv","name":"QuillAudits "},{"id":"xBCwQPoNqNNebqp66bwh","name":"Audita"},{"id":"xGPLXYdLzfpnhjTY041N","name":"Lossless"},{"id":"xz4NJe0c4MkxxfOWsUfB","name":"Uri Kirstein"},{"id":"y0yw20WTl54bUZ1P3ifE","name":"Guardian Audits 🛡"},{"id":"ye84kBumBtofDWaVYybs","name":"Web3 University"},{"id":"zTalP18jdtM22CND06Ae","name":"Chainlink"},{"id":"zljwUffSKJTWUbPFzi5g","name":"CoinFabrik"},{"id":"zsFNjFWBVapIKy3MeoEs","name":"deliriusz"}],"tags":[{"id":"0KTIQ8XLQNZ7NjLkAYXG","name":"Replay Attack"},{"id":"0gjSQN9dAJQ22SJ9AbUP","name":"Portfolio"},{"id":"1ZpOUrVCRnC2tctR2Dos","name":"For Beginners"},{"id":"1p4d4NcqrDESeZ2Zj2mK","name":"Monitoring"},{"id":"1qkCyKhzb4R2PvywFay3","name":"ERC-20"},{"id":"2LYr7ETBRGMBsZylFLOK","name":"Interview"},{"id":"2oaKAn3yRia9KwivRRXS","name":"DAO attacks"},{"id":"37j81peolsCPS7NFZPxH","name":"EIP-1153"},{"id":"3BcULpb81i5PnSfkwfEv","name":"Reentrancy"},{"id":"3L7TImBV3lL8emQekWsR","name":"DeFi Attacks thread"},{"id":"45KNwXbZn7DeL0FrT586","name":"EIP-4844"},{"id":"4FgCNWJs5zNv6PJBLSJg","name":"EIP-1559"},{"id":"4VOo7uOJSF1FJTZJQTCR","name":"Logic Attack"},{"id":"4huJ77CchTXKePBEL5oQ","name":"Hack Analysis"},{"id":"4ucGDXToB9JSirKnnDLX","name":"Rounding error"},{"id":"4zf2u169OeWVxIjQFj6r","name":"Platforms"},{"id":"56mmYOhZuGLoMRclYp7K","name":"Audit Summary"},{"id":"5iuzJs977rpmNTjFafIN","name":"ERC-2771"},{"id":"5o1eNwwICnXL7AAqv5AS","name":"Formal Verification"},{"id":"5t38yISE8SUmYg3dGFj5","name":"Multichain"},{"id":"6QOO5ikGfG8CvTtepJYn","name":"Rewards issue"},{"id":"6Z5kaWZ5huVEYcSU4JXD","name":"Common Attacks Thread"},{"id":"6vW6wt9AusD4nZqXJmh4","name":"Weird tokens"},{"id":"8v7DQgO6KBbg63qbGB53","name":"AI"},{"id":"9rKQBuFwWF1TOjbAppju","name":"Low Level Call Attacks"},{"id":"A12koI5ST471PSxr3422","name":"Input validation "},{"id":"AC1rQoPKjRtafrGYVJUD","name":"Fuzzing"},{"id":"Aes8KjP90PuogF6jHjse","name":"Exercises"},{"id":"AlSZ9iLa4N08U6dWdMk6","name":"EIP-2535"},{"id":"BCy99kIsPcff6W3wSUe0","name":"Bridge"},{"id":"BFf2XPPKgJKC8NlLqqRv","name":"Bug analysis"},{"id":"C5QWI6VOUCkjXxwzN2Qa","name":"Oracles"},{"id":"CWQqmZ7rD27ypYj8UcGQ","name":"Foundry"},{"id":"CoRMGPGivvzljGAvjKPL","name":"Cosmos Blockchain"},{"id":"DKr1xdh3rIt0JvDF5QFP","name":"ERC-7265"},{"id":"DROUrE58Ito1Z3D3vAF5","name":"Sandwitch Attack"},{"id":"EnXEEcU4FJ8Q98fEVm83","name":"Checklists"},{"id":"G3lzaQE8mSv8lZkX2nHq","name":"EIP-1167"},{"id":"GTySlknepq7Hz3WhBGKb","name":"Blockchain thread"},{"id":"Heblu6xlwam2xuZbyReD","name":"Sui"},{"id":"HoFT8In21p0D3jQKZrhn","name":"ERC-1155"},{"id":"HttYPmBNeYVpBnpOl637","name":"ERC-4337"},{"id":"IpWcWz6WXNvSdZMZGgy3","name":"Uniswap"},{"id":"JtsYKSwwNeqpbcVRQOP7","name":"MultiSig"},{"id":"Ka4hrWpckuYMpvEhNqi1","name":"Pools"},{"id":"L3k7kz6w56vWC0UI1ocf","name":"Libraries"},{"id":"LE7I0nRNLElo3WfkWCJU","name":"ERC-7512"},{"id":"LHVf7Z1MfyuG44j7nGdu","name":"Developer Tools"},{"id":"MA6IJ3guocASszmK3xBG","name":"Gas Griefing"},{"id":"MLfLntYg0AzCJP0eusHn","name":"CTF"},{"id":"MTcpTVk1QgX0cER1W1cy","name":"Phishing Attack"},{"id":"N6nnaurtRIJJ1D8SRQUW","name":"Math / Algo"},{"id":"N93ctNBeh4xdnAe1sZgE","name":"Collateral issue"},{"id":"NMkaqjuRvBUoTPh2IT9K","name":"Transaction Explorer"},{"id":"Ob451v7FYrIN6VwsCiuV","name":"DoS attack"},{"id":"PNr8PdU8KeAIpJpi9B7h","name":"Solana"},{"id":"PYUvBBfOhliPpWpRWyls","name":"Project tutorials"},{"id":"Pb6lAavov4EvY35K8NBL","name":"LSD"},{"id":"Phas2C09dCtI5dHOCLGa","name":"Math Attack"},{"id":"PjX7eaJDM08wb31seW0c","name":"DeFi Essentianls"},{"id":"PpSr1fb7uFNRF3xESZ20","name":"Gas Optimization"},{"id":"PynSpmSm4xAipCHRoH7x","name":"DeFi Advanced"},{"id":"R2IyrwFgmPZPYOUhDPfc","name":"Signatures"},{"id":"RM6W06ceEwVgmYLBtxa1","name":"Auditor Mindset"},{"id":"RlRoI1MAKrPEEMSqu1IJ","name":"Options"},{"id":"SUcGbtvKytAKvi91CSE6","name":"ERC-3156"},{"id":"UP83aSCDVmkCEsm9pgCl","name":"Zero Knowledge"},{"id":"Ur0bP7CmQmTVu8h83HqV","name":"Overflow/Underflow Attack"},{"id":"V3ahbBRnjwbjnriZkekS","name":"Longread"},{"id":"V9B2u743lxnEtwSLglsE","name":"Proxy Attack"},{"id":"VbNSPnBXbZdFUkVgCfI5","name":"Code analysis tool/lib"},{"id":"WaQfOgfchOEYIq6UGkUT","name":"Selfdestruct attack"},{"id":"X1rQY0nlQSQ4T71SMvJ5","name":"ERC-1363"},{"id":"X2PsYaGcpjovEyLdQroC","name":"ALL-IN-ONE"},{"id":"Y2UCqaI2W4hlsAtnec2E","name":"Ethernaut"},{"id":"Z0y9e7DpOr6zUT5Jhd9T","name":"TWAP"},{"id":"Z9olDozhwzuk9cNuQcMb","name":"Spot the Bug🐛"},{"id":"ZLWPaY76TpMKdLeRDLmy","name":"ERC thread"},{"id":"a750Jx0G311BHn9NAUm1","name":"EIP-6963"},{"id":"aEThYHPpOQGMC69OLTBe","name":"Signature Database"},{"id":"aVQywMtpDAah4e3Zwut6","name":"ERC-777"},{"id":"aoFmtIlUT6ORD5npksMz","name":"Perpetuals"},{"id":"b3u0cV1FYVAc8PAiy1Ow","name":"⭐ SmartSecHub choice ⭐"},{"id":"bHNj3a0VOsEfxAruyNs4","name":"Code Templates"},{"id":"cWFrvDg3udtF4chqlRh7","name":"POCs"},{"id":"dgFHXShRyXzZbT0ydyhk","name":"EIP-3525"},{"id":"dvnswmE6aEvtFyONIHJQ","name":"Contests/Bounties"},{"id":"eOW5aXSXLezQwBTa3Sbf","name":"CDP"},{"id":"eqjQzxbiZtU1rZrg5DsJ","name":"Bots"},{"id":"fPAmOI5Bcgz4rtIs1cLh","name":"ERC-4626"},{"id":"gNxWb2O2pCs4ESP3I1qY","name":"Flash Loan Attack"},{"id":"giCoYBP7mJ0LTSQpIaZ2","name":"Staking issues"},{"id":"giORPpbdsxXjVcNIdBnt","name":"Miscalculation issue"},{"id":"hGfCFQe7M9kq6MHgd47L","name":"AMM"},{"id":"hXA8feQxEBOaHA664C8G","name":"ERC-6900"},{"id":"i6dAELk3SwqcaHeSEAtO","name":"Proxies / Upgradable"},{"id":"iOWX23T1YTYFHJB5Fge2","name":"ERC-721"},{"id":"iYf6A0I3f4jjQvuEDjC6","name":"Courses"},{"id":"iwYxjmnmWAX7IxhHdyEq","name":"Ethereum / EVM codes"},{"id":"j0xq1D6qMWVzEZgODTu0","name":"DamnVulnerableDeFi"},{"id":"jQsnNnWZXsMRvhSuj6sO","name":"Lending/Borrowing Attack"},{"id":"jSqSiqdmmjRV6F0moQ4b","name":"Threads"},{"id":"kZts0zkNI6SzrEX35vJM","name":"Testing"},{"id":"lMidBLH8oW76XK959oAj","name":"Front Running attack"},{"id":"lrFOZ5lwV7cwiDV4hLTz","name":"Oracle Attacks"},{"id":"lvIvBCizzzf5cSNVRkVV","name":"Compound"},{"id":"nGT53tEvwoW0FbUrQ9cJ","name":"Solidity Essentials"},{"id":"os7d1p4bToBA67WUS1br","name":"EIP-3074"},{"id":"peIkxR4s0AUl50Mrtohh","name":"Smart Contract reader/decoder"},{"id":"pyVp3LyGcsPuxu1EjOFe","name":"Randomness issue"},{"id":"qIS7LSfRljRTH2xIXQOW","name":"DAO / Governance"},{"id":"qpzyvhlsjIUafrBUxRi4","name":"NFT Attacks"},{"id":"r3TtkloePzTRW7QcMV0m","name":"ParadigmCTF"},{"id":"rT97p6Ga7IeLMJu5GgdX","name":"Layer 2"},{"id":"rUUpZOLiiR6z78CJ3EdB","name":"DeFi thread"},{"id":"soMp3rG2bZnvWEUkRU7x","name":"Precision loss issue"},{"id":"tvaECi9G7ZHlNQOMJ4hb","name":"Digest"},{"id":"tz1KDc2Q72FX9uq6qvMG","name":"Invariant Testing"},{"id":"u64Tjgz1sAMitUE1aa7s","name":"Rust"},{"id":"uI1sw2kJPoxTODU5LYIc","name":"Roadmaps"},{"id":"uMOJxy0vN21Xbn5gt8VE","name":"Access Control issue"},{"id":"uVZSKCOuMIMl0G7uYBwi","name":"Defence/Design patterns"},{"id":"vH9apDSFhhaIdCKmWbBc","name":"Quiz"},{"id":"vsBw83D0KlshSS43O3mM","name":"EIP-3448"},{"id":"wA5OodzeKZX2EyZzhdTW","name":"Live Audit"},{"id":"wEEBKN1AyhJDRwQp05iF","name":"ERC-404"},{"id":"wUOhOYBMohFRb0iPSiAq","name":"ERC-6551"},{"id":"xy06rCsyLD9SUt0M6FQd","name":"Misc tools"},{"id":"yK2hVFFQ9QM1alchG81u","name":"EIP-2930"},{"id":"ymn1ZeB3zXJHIlhkAegR","name":"Slippage Attack"},{"id":"yzkgFzIyyz4FarC3LKTt","name":"Audit Report"},{"id":"z1keIgxY6PqdGpyDascv","name":"Assembly/Yul"},{"id":"z5FubOqsgDfodm0RnVBn","name":"Price Manipulation Attack"},{"id":"zYOoubPQ7OaV5l8BfPY3","name":"Cross-Chain thread"},{"id":"zzgs9zWRYClGeQ8BHbBc","name":"LayerZero"}]}
author id - read the article, understand what is the author and try to ding this id in list, if you did not find it, skip it
Twitter ID - read the article, understand what is the author and try to ding this id in list, if you did not find it, if there is custom twitter id in resource add into this field

Links:

https://blog.verichains.io/p/eip-7702-a-double-edged-sword-for
https://blog.verichains.io/p/inside-the-impermax-v3-hack
https://blog.verichains.io/p/rant-exploit-analysis
https://blog.verichains.io/p/the-wetc-double-spend
https://blog.verichains.io/p/audit-the-deployment-not-just-the
https://blog.verichains.io/p/superrare-exploit-analysis
https://blog.verichains.io/p/anatomy-of-a-hack-how-a-simple-logic
https://blog.verichains.io/p/gmx-42m-exploit-root-cause-analysis
https://blog.verichains.io/p/burned-by-design-the-fatal-flaw-behind
https://blog.verichains.io/p/silo-finance-incident-a-costly-test
https://blog.verichains.io/p/free-minting-from-meta-pool-ethereum
https://blog.verichains.io/p/yb-token-hack-analysis
https://blog.verichains.io/p/cork-protocol-exploit-analysis
https://blog.verichains.io/p/mbu-exploit-on-bnb-chain-215m-loss
https://blog.verichains.io/p/multiple-sui-projects-previously
https://blog.verichains.io/p/cetus-protocol-hacked-analysis
https://blog.verichains.io/p/life-protocol-hack-analysis
https://blog.verichains.io/p/step-heros-reentrancy-in-referral
https://blog.verichains.io/p/dcf-token-hack-analysis
